# 服务时长接口变更日志

## 版本 2025-07-01

### 变更概述
修复了 `POST /employee/service-duration/start` 接口的参数验证逻辑，明确区分主订单中的增项服务和追加服务中的增项服务。

### 问题背景
原接口在处理增项服务时存在以下问题：
1. 前端混淆了主订单中的增项服务和追加服务中的增项服务
2. 参数验证不够严格，导致外键约束错误
3. 缺乏明确的使用场景区分

### 接口变更详情

#### 修改的接口
- **接口地址**：`POST /employee/service-duration/start`
- **变更类型**：参数验证逻辑增强，向后兼容

#### 参数变更

**原有参数结构**（存在问题）：
```json
{
  "orderId": 123,
  "recordType": "additional_service",
  "additionalServiceOrderId": 456,    // 所有增项服务都需要此参数
  "additionalServiceId": 789,
  "remark": "开始美容服务"
}
```

**修复后的参数结构**：

1. **主服务**（无变化）：
```json
{
  "orderId": 123,
  "recordType": "main_service",
  "orderDetailId": 456,
  "serviceId": 789,
  "remark": "开始洗护服务"
}
```

2. **主订单中的增项服务**（新增场景）：
```json
{
  "orderId": 123,
  "recordType": "additional_service",
  "orderDetailId": 456,               // 新增：必填
  "additionalServiceId": 789,
  "remark": "开始全身去油服务"
  // 注意：不需要 additionalServiceOrderId
}
```

3. **追加服务中的增项服务**（原有场景，验证增强）：
```json
{
  "orderId": 123,
  "recordType": "additional_service",
  "additionalServiceOrderId": 456,    // 必填
  "additionalServiceId": 789,
  "remark": "开始追加的美容服务"
  // 注意：不需要 orderDetailId
}
```

#### 验证逻辑增强

**主订单中的增项服务验证**：
- 验证 `orderDetailId` 是否存在
- 验证该订单详情是否包含指定的增项服务（查询 `order_detail_additional` 表）
- 验证增项服务是否需要时长统计（`needDurationTracking=true`）

**追加服务中的增项服务验证**：
- 验证 `additionalServiceOrderId` 是否存在
- 验证追加服务订单状态是否为已支付（`status='paid'`）
- 验证该追加服务订单是否包含指定的增项服务（查询 `additional_service_order_details` 表）
- 验证增项服务是否需要时长统计（`needDurationTracking=true`）

#### 错误信息优化

新增的错误提示：
- `"主订单中的增项服务必须提供orderDetailId"`
- `"该订单详情中不包含指定的增项服务"`
- `"追加服务订单不存在"`
- `"只有已支付的追加服务才能开始计时"`
- `"该追加服务订单中不包含指定的增项服务"`

### 响应格式变更

**响应格式保持不变**，但字段含义更明确：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 123,
    "orderId": 178,
    "orderDetailId": 178,                    // 主订单增项服务时有值
    "additionalServiceOrderId": null,        // 追加服务增项时有值
    "employeeId": 7,
    "recordType": "additional_service",
    "serviceId": null,
    "serviceName": "全身去油",
    "additionalServiceId": 2,
    "additionalServiceName": "全身去油",
    "startTime": "2025-07-01T12:55:04.000Z",
    "remark": "开始全身去油服务"
  }
}
```

### 兼容性说明

#### 向后兼容性
- 主服务的调用方式完全不变
- 追加服务中的增项服务调用方式不变，但验证更严格

#### 破坏性变更
- **主订单中的增项服务**：现在必须传递 `orderDetailId` 而不是 `additionalServiceOrderId`
- 如果前端之前错误地为主订单增项服务传递了 `additionalServiceOrderId`，现在会返回明确的错误信息

### 迁移指南

#### 前端代码修改建议

**修改前**（错误的调用方式）：
```javascript
// 错误：主订单增项服务传递了 additionalServiceOrderId
const params = {
  orderId: 178,
  recordType: 'additional_service',
  additionalServiceOrderId: 2,  // 错误！这是增项服务ID，不是追加服务订单ID
  additionalServiceId: 2,
  remark: '开始全身去油服务'
};
```

**修改后**（正确的调用方式）：
```javascript
// 正确：根据增项服务来源选择参数
function startAdditionalService(orderId, additionalServiceId, source, sourceId, remark) {
  const baseParams = {
    orderId,
    recordType: 'additional_service',
    additionalServiceId,
    remark
  };

  if (source === 'original') {
    // 主订单中的增项服务
    return callAPI({
      ...baseParams,
      orderDetailId: sourceId  // sourceId 是 orderDetailId
    });
  } else if (source === 'additional_order') {
    // 追加服务中的增项服务
    return callAPI({
      ...baseParams,
      additionalServiceOrderId: sourceId  // sourceId 是 additionalServiceOrderId
    });
  }
}

// 使用示例
// 主订单中的增项服务
startAdditionalService(178, 2, 'original', 178, '开始全身去油服务');

// 追加服务中的增项服务
startAdditionalService(178, 2, 'additional_order', 456, '开始追加的全身去油服务');
```

### 测试建议

#### 测试用例
1. **主订单增项服务**：
   - 正确参数：传递 `orderDetailId` + `additionalServiceId`
   - 错误参数：传递 `additionalServiceOrderId` 应返回错误

2. **追加服务增项服务**：
   - 正确参数：传递 `additionalServiceOrderId` + `additionalServiceId`
   - 错误参数：传递不存在的 `additionalServiceOrderId` 应返回错误

3. **边界情况**：
   - 增项服务 `needDurationTracking=false` 应返回错误
   - 追加服务订单状态非 `paid` 应返回错误

### 相关文档更新

已更新的文档：
- `docs/service-duration-api.md` - 主要API文档
- `docs/service-duration-tracking.md` - 功能使用说明
- `docs/service-duration-start-api-guide.md` - 详细调用指南

### 后续计划

1. **监控**：观察接口调用情况，确保前端正确适配
2. **优化**：根据使用情况进一步优化错误提示
3. **文档**：持续完善API文档和使用示例
