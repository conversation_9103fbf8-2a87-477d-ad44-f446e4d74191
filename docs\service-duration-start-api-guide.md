# 服务时长开始接口调用指南

## 接口地址
`POST /employee/service-duration/start`

## 概述
该接口用于员工手动开始特定服务项目的计时。支持三种不同的使用场景，需要根据具体情况传递不同的参数。

## 使用场景

### 1. 主服务
开始主订单中的基础服务项目计时。

**请求参数：**
```json
{
  "orderId": 123,
  "recordType": "main_service",
  "orderDetailId": 456,
  "serviceId": 789,
  "remark": "开始洗护服务"
}
```

**参数说明：**
- `orderDetailId`: 必填，订单详情ID
- `serviceId`: 必填，主服务ID

### 2. 主订单中的增项服务
开始主订单下单时选择的增项服务计时。

**请求参数：**
```json
{
  "orderId": 123,
  "recordType": "additional_service",
  "orderDetailId": 456,
  "additionalServiceId": 789,
  "remark": "开始全身去油服务"
}
```

**参数说明：**
- `orderDetailId`: 必填，订单详情ID
- `additionalServiceId`: 必填，增项服务ID
- `additionalServiceOrderId`: 不需要传递

**验证逻辑：**
- 系统会验证该订单详情是否包含指定的增项服务
- 只有 `needDurationTracking=true` 的增项服务才能开始计时

### 3. 追加服务中的增项服务
开始后续追加的服务订单中的增项服务计时。

**请求参数：**
```json
{
  "orderId": 123,
  "recordType": "additional_service",
  "additionalServiceOrderId": 456,
  "additionalServiceId": 789,
  "remark": "开始追加的美容服务"
}
```

**参数说明：**
- `additionalServiceOrderId`: 必填，追加服务订单ID
- `additionalServiceId`: 必填，增项服务ID
- `orderDetailId`: 不需要传递

**验证逻辑：**
- 系统会验证追加服务订单是否存在
- 验证追加服务订单状态是否为已支付（`paid`）
- 验证该追加服务订单是否包含指定的增项服务
- 只有 `needDurationTracking=true` 的增项服务才能开始计时

## 通用参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| recordType | string | 是 | 记录类型：`main_service` 或 `additional_service` |
| remark | string | 否 | 备注信息 |

## 错误处理

### 常见错误及解决方案

1. **"订单不存在"**
   - 检查 `orderId` 是否正确

2. **"只有服务中的订单才能开始服务"**
   - 确保订单状态为"服务中"
   - 需要先调用开始整体订单服务接口

3. **"无权限操作此订单"**
   - 确保当前员工是该订单的负责员工

4. **"该增项服务不需要统计时长"**
   - 只有 `needDurationTracking=true` 的增项服务才能开始计时

5. **"追加服务订单不存在"**
   - 检查 `additionalServiceOrderId` 是否正确
   - 确认是否应该使用主订单中的增项服务场景

6. **"只有已支付的追加服务才能开始计时"**
   - 确保追加服务订单状态为 `paid`

7. **"该订单详情中不包含指定的增项服务"**
   - 检查主订单是否真的包含该增项服务
   - 确认是否应该使用追加服务场景

8. **"该追加服务订单中不包含指定的增项服务"**
   - 检查追加服务订单是否包含该增项服务

## 前端调用示例

### 区分增项服务类型的逻辑

```javascript
// 根据增项服务来源决定调用参数
function startAdditionalService(orderId, additionalServiceId, source, sourceId, remark) {
  const baseParams = {
    orderId,
    recordType: 'additional_service',
    additionalServiceId,
    remark
  };

  if (source === 'original') {
    // 主订单中的增项服务
    return callAPI({
      ...baseParams,
      orderDetailId: sourceId // sourceId 是 orderDetailId
    });
  } else if (source === 'additional_order') {
    // 追加服务中的增项服务
    return callAPI({
      ...baseParams,
      additionalServiceOrderId: sourceId // sourceId 是 additionalServiceOrderId
    });
  }
}

// 使用示例
// 主订单中的增项服务
startAdditionalService(178, 2, 'original', 178, '开始全身去油服务');

// 追加服务中的增项服务
startAdditionalService(178, 2, 'additional_order', 456, '开始追加的全身去油服务');
```

## 响应格式

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 123,
    "orderId": 178,
    "orderDetailId": 178,
    "additionalServiceOrderId": null,
    "employeeId": 7,
    "recordType": "additional_service",
    "serviceName": "全身去油",
    "additionalServiceId": 2,
    "additionalServiceName": "全身去油",
    "startTime": "2025-07-01T12:55:04.000Z",
    "remark": "开始全身去油服务"
  }
}
```

**错误响应：**
```json
{
  "errCode": 500,
  "msg": "追加服务订单不存在",
  "data": null
}
```
